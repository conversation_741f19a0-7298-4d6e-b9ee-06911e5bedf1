import { getDashboardData } from "@/lib/api/dashboard"
import { DashboardMetrics } from "@/components/dashboard/dashboard-metrics"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { DashboardRecentActivity } from "@/components/dashboard/dashboard-recent-activity"
import { ProjectStatusChart } from "@/components/dashboard/project-status-chart"
import { RevenueChart } from "@/components/analytics/revenue-chart"
import { getRevenueChartData } from "@/lib/api/dashboard"
import { Suspense } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

function LoadingCard() {
  return (
    <Card>
      <CardContent className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>
  )
}

async function DashboardContent() {
  try {
    const [dashboardData, revenueData] = await Promise.all([
      getDashboardData(),
      getRevenueChartData()
    ])

    // Convert revenue chart data to analytics format
    const analyticsData = {
      monthlyRevenue: revenueData.map(item => ({
        month: item.name,
        revenue: item.value,
        invoiceCount: 0 // We don't have this data in the simple chart format
      }))
    }

    return (
      <div className="space-y-6">
        {/* Key Metrics Cards */}
        <DashboardMetrics data={dashboardData} />

        {/* Quick Actions */}
        <QuickActions data={dashboardData} />

        {/* Charts Row */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <div className="col-span-4">
            <RevenueChart data={analyticsData} />
          </div>
          <div className="col-span-3">
            <ProjectStatusChart data={dashboardData} />
          </div>
        </div>

        {/* Recent Activity */}
        <DashboardRecentActivity data={dashboardData} />
      </div>
    )
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <p className="text-muted-foreground">
            Failed to load dashboard data. Please try again later.
          </p>
        </CardContent>
      </Card>
    )
  }
}

export default function DashboardPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">
            Welcome back! Here&apos;s what&apos;s happening with your business today.
          </p>
        </div>
      </div>

      <Suspense fallback={<LoadingCard />}>
        <DashboardContent />
      </Suspense>
    </div>
  )
}
