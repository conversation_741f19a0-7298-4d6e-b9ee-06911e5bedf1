'use client'

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AnalyticsData } from "@/lib/api/analytics"
import { InvoiceWithClient, ProjectWithClient, Client } from "@/lib/types"
import { formatDistanceToNow } from "date-fns"
import Link from "next/link"

interface RecentActivityProps {
  data: AnalyticsData
}

export function RecentActivity({ data }: RecentActivityProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'lead':
        return 'bg-blue-100 text-blue-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'planning':
        return 'bg-yellow-100 text-yellow-800'
      case 'review':
        return 'bg-purple-100 text-purple-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  return (
    <div className="grid gap-4 md:grid-cols-3">
      {/* Recent Invoices */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Invoices</CardTitle>
          <CardDescription>Latest invoice activity</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {data.recentActivity.recentInvoices.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent invoices</p>
          ) : (
            data.recentActivity.recentInvoices.map((invoice: InvoiceWithClient) => (
              <div key={invoice.id} className="flex items-center justify-between space-x-2">
                <div className="flex-1 min-w-0">
                  <Link 
                    href={`/invoices/${invoice.id}`}
                    className="text-sm font-medium hover:underline truncate block"
                  >
                    {invoice.invoice_number}
                  </Link>
                  <p className="text-xs text-muted-foreground truncate">
                    {invoice.client?.company || invoice.client?.name || 'Unknown Client'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(invoice.created_at), { addSuffix: true })}
                  </p>
                </div>
                <div className="flex flex-col items-end space-y-1">
                  <Badge className={`text-xs ${getStatusColor(invoice.status)}`}>
                    {formatStatus(invoice.status)}
                  </Badge>
                  <span className="text-xs font-medium">
                    {formatCurrency(invoice.total_amount)}
                  </span>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Recent Projects */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Projects</CardTitle>
          <CardDescription>Latest project activity</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {data.recentActivity.recentProjects.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent projects</p>
          ) : (
            data.recentActivity.recentProjects.map((project: ProjectWithClient) => (
              <div key={project.id} className="flex items-center justify-between space-x-2">
                <div className="flex-1 min-w-0">
                  <Link 
                    href={`/projects/${project.id}`}
                    className="text-sm font-medium hover:underline truncate block"
                  >
                    {project.name}
                  </Link>
                  <p className="text-xs text-muted-foreground truncate">
                    {project.client?.company || project.client?.name || 'Unknown Client'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(project.created_at), { addSuffix: true })}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <Badge className={`text-xs ${getStatusColor(project.status)}`}>
                    {formatStatus(project.status)}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Recent Clients */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Clients</CardTitle>
          <CardDescription>Latest client activity</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {data.recentActivity.recentClients.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent clients</p>
          ) : (
            data.recentActivity.recentClients.map((client: Client) => (
              <div key={client.id} className="flex items-center justify-between space-x-2">
                <div className="flex-1 min-w-0">
                  <Link 
                    href={`/clients/${client.id}`}
                    className="text-sm font-medium hover:underline truncate block"
                  >
                    {client.name}
                  </Link>
                  {client.company && (
                    <p className="text-xs text-muted-foreground truncate">
                      {client.company}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(client.created_at), { addSuffix: true })}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <Badge className={`text-xs ${getStatusColor(client.status)}`}>
                    {formatStatus(client.status)}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  )
}
