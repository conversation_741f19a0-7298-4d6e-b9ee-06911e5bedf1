import { createClient } from '@/lib/supabase/server'
import { Invoice, Project, Client } from '@/lib/types'

export interface AnalyticsData {
  revenue: {
    total: number
    thisMonth: number
    lastMonth: number
    growth: number
  }
  invoices: {
    total: number
    paid: number
    pending: number
    overdue: number
    paidPercentage: number
  }
  clients: {
    total: number
    active: number
    leads: number
    inactive: number
    newThisMonth: number
  }
  projects: {
    total: number
    active: number
    completed: number
    in_progress: number
    completionRate: number
  }
  recentActivity: {
    recentInvoices: Invoice[]
    recentProjects: Project[]
    recentClients: Client[]
  }
  monthlyRevenue: Array<{
    month: string
    revenue: number
    invoiceCount: number
  }>
  serviceTypes: Array<{
    service_type: string
    count: number
    percentage: number
  }>
}

export async function getAnalyticsData(): Promise<AnalyticsData> {
  const supabase = await createClient()

  // Get current date info
  const now = new Date()
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()
  const firstDayThisMonth = new Date(currentYear, currentMonth, 1).toISOString()
  const firstDayLastMonth = new Date(currentYear, currentMonth - 1, 1).toISOString()
  const firstDayNextMonth = new Date(currentYear, currentMonth + 1, 1).toISOString()

  // Fetch all data in parallel
  const [
    invoicesResult,
    clientsResult,
    projectsResult,
    recentInvoicesResult,
    recentProjectsResult,
    recentClientsResult,
    monthlyRevenueResult
  ] = await Promise.all([
    // All invoices
    supabase
      .from('invoices')
      .select('id, status, total_amount, created_at, invoice_date')
      .order('created_at', { ascending: false }),

    // All clients
    supabase
      .from('clients')
      .select('id, status, created_at')
      .order('created_at', { ascending: false }),

    // All projects
    supabase
      .from('projects')
      .select('id, status, service_type, created_at, budget')
      .order('created_at', { ascending: false }),

    // Recent invoices (last 5)
    supabase
      .from('invoices')
      .select(`
        id, invoice_number, status, total_amount, created_at,
        client:clients(name, company)
      `)
      .order('created_at', { ascending: false })
      .limit(5),

    // Recent projects (last 5)
    supabase
      .from('projects')
      .select(`
        id, name, status, created_at,
        client:clients(name, company)
      `)
      .order('created_at', { ascending: false })
      .limit(5),

    // Recent clients (last 5)
    supabase
      .from('clients')
      .select('id, name, company, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5),

    // Monthly revenue for the last 12 months
    supabase
      .from('invoices')
      .select('total_amount, invoice_date, status')
      .gte('invoice_date', new Date(currentYear - 1, currentMonth, 1).toISOString().split('T')[0])
      .eq('status', 'paid')
      .not('invoice_date', 'is', null)
      .order('invoice_date', { ascending: true })
  ])

  // Handle errors
  if (invoicesResult.error) throw new Error(`Failed to fetch invoices: ${invoicesResult.error.message}`)
  if (clientsResult.error) throw new Error(`Failed to fetch clients: ${clientsResult.error.message}`)
  if (projectsResult.error) throw new Error(`Failed to fetch projects: ${projectsResult.error.message}`)

  const invoices = invoicesResult.data || []
  const clients = clientsResult.data || []
  const projects = projectsResult.data || []
  const recentInvoices = recentInvoicesResult.data || []
  const recentProjects = recentProjectsResult.data || []
  const recentClients = recentClientsResult.data || []
  const monthlyRevenueData = monthlyRevenueResult.data || []

  // Calculate revenue metrics
  const paidInvoices = invoices.filter(inv => inv.status === 'paid')
  const totalRevenue = paidInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)
  
  const thisMonthInvoices = paidInvoices.filter(inv => 
    new Date(inv.invoice_date) >= new Date(firstDayThisMonth) &&
    new Date(inv.invoice_date) < new Date(firstDayNextMonth)
  )
  const thisMonthRevenue = thisMonthInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)
  
  const lastMonthInvoices = paidInvoices.filter(inv => 
    new Date(inv.invoice_date) >= new Date(firstDayLastMonth) &&
    new Date(inv.invoice_date) < new Date(firstDayThisMonth)
  )
  const lastMonthRevenue = lastMonthInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)
  
  const revenueGrowth = lastMonthRevenue > 0 
    ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
    : 0

  // Calculate invoice metrics
  const paidCount = invoices.filter(inv => inv.status === 'paid').length
  const pendingCount = invoices.filter(inv => ['draft', 'sent'].includes(inv.status)).length
  const overdueCount = invoices.filter(inv => inv.status === 'overdue').length
  const paidPercentage = invoices.length > 0 ? (paidCount / invoices.length) * 100 : 0

  // Calculate client metrics
  const activeClients = clients.filter(client => client.status === 'active').length
  const leadClients = clients.filter(client => client.status === 'lead').length
  const inactiveClients = clients.filter(client => client.status === 'inactive').length
  const newClientsThisMonth = clients.filter(client => 
    new Date(client.created_at) >= new Date(firstDayThisMonth)
  ).length

  // Calculate project metrics
  const activeProjects = projects.filter(project => project.status === 'in_progress').length
  const completedProjects = projects.filter(project => project.status === 'completed').length
  const inProgressProjects = projects.filter(project => project.status === 'in_progress').length
  const completionRate = projects.length > 0 ? (completedProjects / projects.length) * 100 : 0

  // Calculate monthly revenue breakdown
  const monthlyRevenue = []
  for (let i = 11; i >= 0; i--) {
    const date = new Date(currentYear, currentMonth - i, 1)
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)
    
    const monthData = monthlyRevenueData.filter(inv => {
      const invDate = new Date(inv.invoice_date)
      return invDate >= monthStart && invDate <= monthEnd
    })
    
    const monthRevenue = monthData.reduce((sum, inv) => sum + inv.total_amount, 0)
    
    monthlyRevenue.push({
      month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      revenue: monthRevenue,
      invoiceCount: monthData.length
    })
  }

  // Calculate service type distribution
  const serviceTypeCounts = projects.reduce((acc, project) => {
    if (project.service_type) {
      acc[project.service_type] = (acc[project.service_type] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)

  const serviceTypes = Object.entries(serviceTypeCounts).map(([service_type, count]) => ({
    service_type,
    count,
    percentage: projects.length > 0 ? (count / projects.length) * 100 : 0
  }))

  return {
    revenue: {
      total: totalRevenue,
      thisMonth: thisMonthRevenue,
      lastMonth: lastMonthRevenue,
      growth: revenueGrowth
    },
    invoices: {
      total: invoices.length,
      paid: paidCount,
      pending: pendingCount,
      overdue: overdueCount,
      paidPercentage
    },
    clients: {
      total: clients.length,
      active: activeClients,
      leads: leadClients,
      inactive: inactiveClients,
      newThisMonth: newClientsThisMonth
    },
    projects: {
      total: projects.length,
      active: activeProjects,
      completed: completedProjects,
      in_progress: inProgressProjects,
      completionRate
    },
    recentActivity: {
      recentInvoices,
      recentProjects,
      recentClients
    },
    monthlyRevenue,
    serviceTypes
  }
}
